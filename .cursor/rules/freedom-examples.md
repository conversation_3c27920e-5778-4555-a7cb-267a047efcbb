---
description: 
globs: 
alwaysApply: false
---
# Exemplos Práticos - Projeto Freedom

## ?? Referências de Código

### Exemplos Completos de Telas
- **Frota Cliente**: [FrmFrotaClienteA.java](mdc:crmservice/crmservice/crmservice/crmservice/src/main/java/freedom/bytecode/form/FrmFrotaClienteA.java) + [FrotaClienteRNA.java](mdc:crmservice/crmservice/crmservice/crmservice/src/main/java/freedom/bytecode/rn/FrotaClienteRNA.java)
- **CRM Questionário**: [FrmCrmQuestionarioA.java](mdc:crmservice/crmservice/crmservice/crmservice/src/main/java/freedom/bytecode/form/FrmCrmQuestionarioA.java) + [CrmQuestionarioRNA.java](mdc:crmservice/crmservice/crmservice/crmservice/src/main/java/freedom/bytecode/rn/CrmQuestionarioRNA.java)

## ?? Templates de Implementação

### Template: Evento de Botão
```java
// Em FrmNomeTelaA.java
@Override
public void nomeComponenteEvento(Event<Object> event) {
        try {
        rn.carregarTabela();
    } catch (DataException e) {
        CrmService.showErros("Erro ao carregar tabela", e)
    }
}

private boolean validarCampos() {
    // Validações específicas da UI
    return true;
}



```

### Template: Evento de Mudança (Change)
```java
// Em FrmNomeTelaA.java
@Override
public void comboNomeChange(Event<Object> event) {
    try {
        Object valorSelecionado = comboNome.getValue()
        if (valorSelecionado != null) {
            // Carregar dados relacionados
            rn.carregarDados(params);
        }
    } catch (DataException e) {
        showError("Erro ao processar mudança: " + e.getMessage());
    }
}
```

### Template: Regra de Negócio com Cursor
##documentação @freedom-cursors.mdc
```java
// Em FrmNomeTelaRNA.java  
public void filtrarTbCursor() throws DataException {
    tbCursorExistente.close();
    tbCursorExistente.clearFilters();
    tbCursorExistente.clearParams();
    tbCursorExistente.open();
}
```

### Template: Filtrar Dados Relacionados
```java
// Em NomeRNA.java
public void filtrarDados(int codigo, boolean somenteAtivos) throws DataException {
    tbCursor.close();
    tbCursor.clearFilters()
    tbCursor.clearParams()
    tbCursor.setFilterCODIGO(codigo);
    tbCursor.setFilterATIVOS(somenteAtivos ? "S" : "N" );
    tbCursor.open();
}
```

## ?? Padrões Comuns de Validação

### Validação de Campos Obrigatórios
```java
private boolean validarCamposObrigatorios() {
    if (edtNome.getText().trim().isEmpty()) {
        showError("Campo Nome é obrigatório!");
        edtNome.focus();
        return false;
    }
    
    if (cbbTipo.getSelectedIndex() == -1) {
        showError("Selecione um tipo!");
        cbbTipo.focus();
        return false;
    }
    
    return true;
}
```

### Validação de Formato
```java
private boolean validarFormatos() {
    String email = edtEmail.getText().trim();
    if (!email.isEmpty() && !email.contains("@")) {
        showError("Email inválido!");
        edtEmail.focus();
        return false;
    }
    
    return true;
}
```

## ?? Padrões de Navegação e Estados

### Controle de Botões por Estado
```java
private void habilitarBotoes(boolean alterar) {
    btnNovo.setEnabled(!alterar);
    btnAlterar.setEnabled(!alterar);
    btnSalvar.setEnabled(alterar);
    btnCancelar.setEnabled(alterar);
    
    // Habilitar/desabilitar campos
    edtNome.setEnabled(alterar);
    cbbTipo.setEnabled(alterar);
}
```

### Limpar Tela
```java
private void limparTela() {
    edtNome.setValue("");
    cbbTipo.clear();
    edtObservacao.setValue("");

    // Focar no primeiro campo
    edtNome.focus();
}
```

## ?? Solicitações Típicas e Respostas

### Cenário: "Adicionar validação ao salvar"
```java
ANÁLISE: ? Método btnSalvarClick já existe em FrmNome.java
AÇÃO: Implementar validação em FrmNomeA.java

// Código a ser adicionado:
@Override
public void btnSalvarClick(final Event<Object> event) {
    if (validarCamposObrigatorios() && validarFormatos()) {
        // Continuar com salvamento...
    }
}
```

### Cenário: "Filtrar combo por seleção de outro combo"
```java
ANÁLISE: ? Eventos onChange já existem em FrmNome.java
AÇÃO: Implementar em FrmNomeA.java + criar método em RNA.java

// Em FrmNomeA.java:
@Override
public void cbbPrincipalChange(Event<Object> event) {
    Object valor = cbbPrincipal.getSelectedValue();
    if (valor != null) {
        getRN(NomeRNA.class).filtrarSecundario((Integer) valor);
    }
}

// Em NomeRNA.java:
public void filtrarSecundario(int codigo) throws DataException {
    // Usar cursor já disponível
}
```

Estes exemplos servem como base para implementações similares no projeto.





