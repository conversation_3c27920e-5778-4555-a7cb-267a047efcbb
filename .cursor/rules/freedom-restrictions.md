---
description: 
globs: 
alwaysApply: false
---
# Restrições Críticas - Projeto Freedom

## ❌ ARQUIVOS QUE A IA NÃO PODE ALTERAR

### 📄 Arquivos de Design
- **Todos os `.frm`** em ](mdc:crmservice/design/) - Apenas para adicionar componentes UI
- **EXCEÇÃO**: Pode adicionar componentes UI, mas NUNCA cursores

### 🔧 Classes Wizard (Geradas Automaticamente)
- **Form WizarrmNomeW.java` em [wizard/](mdc:crmservice/crmservmain/java/freedom/bytecode/form/wizard)
- **RN Wizards**: `NomeRNW.java` em [wizard/](mdc:crmservice/crmservice/src/main/javecode/rn/wizard)
- **Classes Base**: `FrmNome.java` (abstratas geradas do .frm)

### 📊 Módulo de Cursores
- **TODO o módulo** [cursores-zk/](mdc:crmservice/cursores-zk)
- **Razão**: Cursores são estruturas críticas do banco de dados
- **Ação**: Se precisar de novo cursor, solicitar intervenção manual

### 🏗️ Classes Abstratas Base
- Todas as classes que estendem diretamente do framework Freedom
- s com comentário "Gerado automaticamente"

## ✅ ARQUIVOS QUE A IA Implementações Concretas de Form
- **Padrão**: `FrmNomeA.java` em [form/](mdc:crmservice/mdcservice/src/main/java/freedom/bytecode/form)
- **Função**: Implementar eve- **Exemplo**: [FrmFrotaClienteA.java](mdc:crmservice/crmservice/src/main/java/freedom/bytecode/form/FrmFrotaClienteA.java)

### 🧠 Implementações de Regras de Negócio
- **Padrão**: `NomeRNA.java` em [rn/](mdc:crmservice/crmservice/src/main/java/freedom/bytecode/rn)
- **Função**: Lógica de negócio e manipulação de cursores
- **Exemplo**: [FrotaClienteRNA.java](mdc:crmservice/crmservice/src/main/java/freedom/bytecode/rn/FrotaClienteRNA.java)

### 🎨 Componentes UI (com restrições)
- **Arquivo**: `.frm` para adicionar novos componentes
- **Restrição**: Apenas componentes UI, NUNCA cursores
- **Processo**: Adicionar → Regenerar classes base → Implementar lógica

## 🔍 Como Identificar o Tipo de Arquivo

### ❌ NÃO ALTERAR se contém:
```java
// Comentários indicando geração automática
/* Gerado automaticamente */
/* Data Changed: */

// Herança direta do framework
extends TFForm
implements IRegraNegocio

// Nomes terminados em W (Wizard)
class FrmNomeW extends FrmNome
class NomeRNW extends NomeRN
```

### ✅ PODE ALTERAR se contém:
```java
// Herança de classes W (Wizard)
class FrmNomeA extends FrmNomeW
class NomeRNA extends NomeRNW

// Implementações concretas de métodos abstratos
@Override
public void metodoEvento(Event<Object> event) {
// ignore o isAccess() não precisa validar isso na implementação
```

## 🚨 Sinais de Alerta

### Parar Imediatamente se:
1. **Arquivo está no módulo cursores-zk**
2. **Classe termina com 'W' (Wizard)**
3. **Comentário menciona geração automática**
4. **Herda diretamente do framework Freedom**
5. **Solicitação envolve criar novo cursor**

### Resposta Obrigatória:
```
🚫 OPERAÇÃO NÃO PERMITIDA

ARQUIVO: [nome do arquivo]
MOTIVO: [específico - wizard/gerado/cursor/etc]
AÇÃO NECESSÁRIA: [intervenção manual/usar existente/etc]

A IA não tem permissão para alterar este tipo de arquivo.
```

## 📋 Checklist Antes de Alterar

1. ✅ **Arquivo é de implementação concreta?** (termina com 'A')
2. ✅ **Não está no módulo cursores-zk?**
3. ✅ **Não é classe wizard?** (não termina com 'W')
4. ✅ **Não tem comentários de geração automática?**
5. ✅ **Componentes/cursores necessários já existem na classe base?**

**Se TODOS os itens forem ✅, pode prosseguir com a alteração.**

## 🛡️ Proteções de Segurança

- **Sempre verificar** a classe base antes de implementar
- **Nunca assumir** que um cursor existe sem verificar
- **Sempre usar** cursores já instanciados na hierarquia
- **Sempre que alterar o .frm** não tentar gerar/buildar para gerar os arquivos wizards, essa ação precisa ser feita por um humano, então apenas altarar o .frm e após o humano confirmar, continuar as implementações do FrmNomeTelaA.java e FrmNomeTelaRNA.java
- **Documentar** alterações feitas nas classes concretas