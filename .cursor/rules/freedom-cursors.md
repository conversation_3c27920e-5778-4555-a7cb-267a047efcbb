---
description: 
globs: 
alwaysApply: false
---
# Sistema de Cursores - Projeto Freedom

## Localização e Estrutura

- **Módulo**: [cursores-zk](mdc:crmservice/crmservice/crmservice/cursores-zk/src/main/java/freedom/bytecode/cursor)
- **<PERSON><PERSON><PERSON>**: `NOME_DO_CURSOR.java`
- **?? CRÍTICO**: Cursores NÃO podem ser criados ou editados, apenas utilizados

## Padrões de Uso
### caso o cursor não esteja instanciado em FrmNome.java, Instanciação (sempre no construtor ou métodos da RNA)
```java
CURSOR_NAME tbNomeInstancia = new CURSOR_NAME("nomeInstancia");
```

## IMPORTANTE
ao encontrar a classe do cursor CURSOR_NAME
buscar e obter oq for necessário, na classe tem todos os campos (get,set) e tabém todos os filtros (setFilterName)

### Operações Comuns
```java
// Ciclo básico
tbCursor.open();           // Abrir cursor
tbCursor.first();          // Primeiro registro
tbCursor.next();           // Próximo registro  
tbCursor.close();          // Fechar cursor

// Filtragem e consulta
//com nome no metodo (preferivel)
tbCursor.setFilterFILTER_NAME(params);  // Filtrar dados
//filtro sem nome
tbCursor.addFilter(NOME_FILTRO);
tbCursor.addParam(params);

// Acesso aos dados
//com nome (preferivel)
tbCursor.getCAMPO_NAME();
tbCursor.setCAMPO_NAME(valor);
//sem nome
tbCursor.getFieldValue("CAMPO_NAME");
tbCursor.setFieldValue("CAMPO_NAME", valor);
```

### Exemplos de Cursores Frequentes

**CRM e Clientes:**
- `CLIENTES_FROTA` - Dados da frota de clientes
- `CLIENTE_DIVERSO` - Informações adicionais de clientes
- `CLIENTE_CONTATO` - Contatos dos clientes

**Produtos e Catálogo:**
- `PRODUTOS` - Catálogo de produtos
- `PRODUTOS_MODELOS` - Modelos de produtos
- `MARCAS` - Marcas disponíveis

**Ordem de Serviço:**
- `OS_AGENDA` - Agendamentos de OS
- `OS_ITEM_GRID` - Itens da OS
- `MOB_OS_PERTENCE` - Relacionamentos OS

**Documentos:**
- `DOC_POR_CHASSI` - Documentos por chassi

## Estrutura Interna dos Cursores

Cada cursor contém:
```java
public final class NOME_CURSOR extends TFTable {
    // Construtor obrigatório com instanceName
    public NOME_CURSOR(String instanceName) {
        setName(instanceName);
        setCursor("id_numerico");   
        setTableName("NOME_TABELA");
        
        // Definição dos campos
        TFTableField campo = new TFTableField();
        campo.setName("NOME_CAMPO");
        campo.setFieldType("ftString|ftInteger|ftDecimal|ftDate");
        campo.setPrimaryKey(true|false);
        // ...
    }
}
```

## Restrições Importantes

### ? IA NÃO PODE:
- Criar novos cursores
- Editar cursores existentes
- Alterar estrutura de campos
- Modificar módulo cursores-zk

### ? IA PODE:
- Usar cursores existentes nas classes RNA
- Instanciar cursores já definidos
- Chamar métodos dos cursores
- Filtrar e consultar dados

## Padrões de Busca

Para encontrar cursores disponíveis:
```bash
# Buscar por nome específico
find cursores-zk/ -name "*CLIENTE*"

# Listar todos os cursores
ls cursores-zk/src/main/java/freedom/bytecode/cursor/

# Buscar uso de cursor em RNA
grep -r "new NOME_CURSOR" crmservice/src/main/java/freedom/bytecode/rn/
```

## Exemplo Prático

No [FrotaClienteRNA.java](mdc:crmservice/crmservice/crmservice/crmservice/src/main/java/freedom/bytecode/rn/FrotaClienteRNA.java):
```java
// Instanciação
CLIENTE_DIVERSO tbClienteDiversos = new CLIENTE_DIVERSO("tbClienteDiversos");

// Uso em método
public String getNomeCliente(Double codCliente) throws DataException {
    tbClienteDiversos.filtrar("COD_CLIENTE", codCliente);
    if (tbClienteDiversos.exists()) {
        return tbClienteDiversos.getFieldValue("NOME").toString();
    }
    return "";
}
```

princiapais funções dos cursores
```java
     /**
     * Se simplemesmente der o cancel e for registro novo adicionado com o append ele deleta o registro
     * então crio uma variavel de beckup, para quando eu editar o registro e der o cancelar, não precisar utilizar
     * o table.cancel do cursor
     */
    @Getter
    @Setter
    private RowType backupRowtbClienteDiverso = null;

    /**
     * Adiciona novo registro no table tbClienteDiverso
     */
    public void incluirNovoTbClienteDiverso() throws DataException{
        /* salva backup do registro para utilizar na ação de cancelar alterações */
        this.tbClienteDiverso.disableControls();
        this.tbClienteDiverso.append();
        /* caso pegue id de uma sequence */
        //this.tbClienteDiverso.setID(SequenceUtil.nextVal("NOME_SEQUENCE"));
        this.tbClienteDiverso.post();
        this.tbClienteDiverso.enableControls();
    }

    /**
     * Função habilita a edição do table tbClienteDiverso e salva um beckup do registro a ser alterado,
     * para caso cancele as ações volte para o estado de backup o registro;
     */
    public void editarTbClienteDiverso() throws DataException{
        /* salva backup do registro para utilizar na ação de cancelar alterações */
        RowType backupTbClienteDiverso = this.tbClienteDiverso.toRowType();
        this.setBackupRowtbClienteDiverso(backupTbClienteDiverso);
        this.tbClienteDiverso.edit();
    }


    public boolean tbClienteDiversoIsEmpty(){
        return  !this.tbClienteDiverso.isActive() ||  this.tbClienteDiverso.isEmpty();
    }

    /**
     * Função cancela as alterações do registro da tabela ClienteDiverso  voltando para o estado de backup do registro
     */
    public void cancelarEdicaoRegistroTbClienteDiverso(RowType backupTbClienteDiverso) throws DataException{
        // caso esteja inserindo um novo registro então apenas deletamos
        if (this.tbClienteDiverso.getRowState() == RowState.INSERTED){
            this.tbClienteDiverso.delete();
            return;
        }

        // caso não esteja inserindo então damos o revert de acordo com o backup
        /* Recupera backup do registro */
        RowType backupTbClienteDiversoTemp = this.getBackupRowtbClienteDiverso();
        /* Ao invés de utilizar o cancel, cancelo as alterações do registro com o backup do registro salvo ao editar a tabela */
        String backupConcluido = TableUtil.restaurarRegistroComBackup(this.tbClienteDiverso, backupTbClienteDiversoTemp);
        if(!backupConcluido.isEmpty()){
            throw new DataException(backupConcluido);
        }
        TableUtil.restaurarRegistroComBackup(tbClienteDiverso, backupTbClienteDiversoTemp);
        this.tbClienteDiverso.post();
        /* limpo o registro de beckup */
        this.setBackupRowtbClienteDiverso(null);
    }

    /**
     * Cancela todas as operações da tabela
     * @throws DataException
     */
    public void cancelarEdicaoTbClienteDiverso() throws DataException{
        this.tbClienteDiverso.cancel();
        this.tbClienteDiverso.cancelUpdates();
    }

    /**
     * salva as alterações do registro $NOMETABELA
     */
    public void salvarEdicaoTbClienteDiverso() throws DataException{
        this.tbClienteDiverso.post();
    }

    /**
     * salva as alterações do registro $NOMETABELA
     */
    public void commitarEdicaoTbClienteDiverso() throws DataException{
        this.tbClienteDiverso.post();
        this.tbClienteDiverso.applyUpdates();
        this.tbClienteDiverso.commitUpdates();
    }

    public void deletarTbClienteDiverso() throws DataException{
        /* caso tenha tabela filhos */
    	/*
    	tbFilho.first();
    	while (!tbFilho.eof()) {
    		deletarTbFilho();
    	}
    	*/
        this.tbClienteDiverso.delete();
    }
```





