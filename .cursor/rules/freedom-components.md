# Componentes Freedom ZK - Guia de Referência

## Estrutura Básica de um Formulário

```pascal
object NomeFormulario: TFForm
  Left = 44
  Top = 163
  ActiveControl = FVBox1
  BorderIcons = [biSystemMenu, biMinimize]
  Caption = 'Título da Tela'
  ClientHeight = 497
  ClientWidth = 798
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'Tahoma'
  Font.Style = []
  OldCreateOrder = False
  WOwner = FrWizard
  WOrigem = EhMain
  WKey = 'chave_unica'
  InterfaceRN = 'NomeTelaRN'
  Access = False
  
  object componenteFilho: TipoComponente
    // propriedades do componente
  end
end
```

---

## TFVBox - Container Vertical

**Descrição:** Container principal que organiza componentes verticalmente usando layout flexbox. É o componente de layout mais utilizado como container raiz de formulários.

### Propriedades

| Propriedade | Tipo | Descrição |
|-------------|------|-----------|
| `Left`, `Top`, `Width`, `Height` | Integer | Posição e dimensões do container |
| `Align` | TAlign | Alinhamento no container pai (alClient, alTop, alBottom, alLeft, alRight) |
| `AutoWrap` | Boolean | Quebra automática de linha (geralmente False para layouts controlados) |
| `BevelKind` | TBevelKind | Tipo de borda visual (bkTile mais comum, bkNone para invisível) |
| `BevelOuter` | TBevelOuter | Borda externa (bvNone mais comum para layouts limpos) |
| `BorderStyle` | TBorderStyle | Estilo da borda (stNone para containers de layout) |
| `Caption` | String | Texto do container (sempre ' ' para containers visuais) |
| `FlowStyle` | TFlowStyle | Direção do fluxo (fsTopBottomLeftRight para organização vertical) |
| `Padding.Top/Left/Right/Bottom` | Integer | Espaçamento interno em pixels entre a borda e o conteúdo |
| `Margin.Top/Left/Right/Bottom` | Integer | Espaçamento externo em pixels do container |
| `Spacing` | Integer | Espaçamento entre elementos filhos em pixels |
| `TabOrder` | Integer | Ordem de tabulação do container |
| `Flex.Vflex` | TFlexType | Flexibilidade vertical (ftTrue=cresce, ftFalse=fixo, ftMin=mínimo) |
| `Flex.Hflex` | TFlexType | Flexibilidade horizontal (ftTrue=cresce, ftFalse=fixo, ftMin=mínimo) |
| `Scrollable` | Boolean | Permite scroll quando conteúdo excede o tamanho |
| `WOwner` | TWOwner | Proprietário (FrInterno para filhos, FrWizard para raiz) |
| `WOrigem` | TWOrigem | Origem (EhNone para filhos, EhMain para raiz) |

### Exemplo Prático

```pascal
object FVBox1: TFVBox
  Left = 0
  Top = 0
  Width = 798
  Height = 497
  Align = alClient
  AutoWrap = False
  BevelKind = bkTile
  BevelOuter = bvNone
  BorderStyle = stNone
  Caption = ' '
  FlowStyle = fsTopBottomLeftRight
  Padding.Top = 5
  Padding.Left = 5
  Padding.Right = 5
  Padding.Bottom = 5
  TabOrder = 0
  Spacing = 8
  Flex.Vflex = ftTrue
  Flex.Hflex = ftTrue
  Scrollable = True
  WOwner = FrInterno
  WOrigem = EhNone
end
```

---

## TFHBox - Container Horizontal

**Descrição:** Container que organiza componentes horizontalmente. Ideal para barras de botões, filtros em linha e agrupamento horizontal de campos.

### Propriedades

| Propriedade | Tipo | Descrição |
|-------------|------|-----------|
| `Left`, `Top`, `Width`, `Height` | Integer | Posição e dimensões do container |
| `Align` | TAlign | Alinhamento no container pai |
| `AutoWrap` | Boolean | Quebra automática quando excede largura |
| `BevelKind`, `BevelOuter`, `BorderStyle` | Enum | Configurações de borda (mesmo que VBox) |
| `Caption` | String | Texto do container (sempre ' ') |
| `Padding.Top/Left/Right/Bottom` | Integer | Espaçamento interno |
| `Spacing` | Integer | Espaçamento entre elementos filhos |
| `VAlign` | TVAlign | Alinhamento vertical dos filhos (tvTop, tvCenter, tvBottom) |
| `Flex.Vflex/Hflex` | TFlexType | Flexibilidade do container |
| `WOwner`, `WOrigem` | TWOwner/TWOrigem | Configurações de ownership |

### Exemplo Prático

```pascal
object hBoxBotoes: TFHBox
  Left = 0
  Top = 0
  Width = 750
  Height = 65
  AutoWrap = False
  BevelKind = bkTile
  BevelOuter = bvNone
  BorderStyle = stNone
  Caption = ' '
  Padding.Top = 5
  Padding.Left = 5
  Padding.Right = 5
  Padding.Bottom = 0
  TabOrder = 0
  Spacing = 3
  Flex.Vflex = ftFalse
  Flex.Hflex = ftTrue
  Scrollable = False
  WOwner = FrInterno
  WOrigem = EhNone
  VAlign = tvTop
end
```

---

## TFGroupbox - Caixa de Grupo

**Descrição:** Container com borda visual e título para agrupar componentes relacionados. Usado para criar seções organizadas em formulários.

### Propriedades

| Propriedade | Tipo | Descrição |
|-------------|------|-----------|
| `Caption` | String | Título exibido no cabeçalho do grupo |
| `Font.Charset/Color/Height/Name/Style` | TFont | Configurações da fonte do título |
| `ParentFont` | Boolean | Herdar fonte do componente pai |
| `Padding.Top/Left/Right/Bottom` | Integer | Espaçamento interno do conteúdo |
| `Flex.Vflex/Hflex` | TFlexType | Flexibilidade do grupo |
| `Scrollable` | Boolean | Permitir scroll interno |
| `Closable` | Boolean | Permitir fechar/expandir o grupo |
| `Closed` | Boolean | Estado inicial (fechado=True, aberto=False) |
| `Orient` | TOrientation | Orientação (coHorizontal, coVertical) |
| `Style` | TGroupStyle | Estilo visual (grp3D para efeito 3D) |
| `HeaderImageId` | Integer | ID da imagem exibida no cabeçalho |

### Exemplo Prático

```pascal
object groupboxtbQuestionario: TFGroupbox
  Left = 0
  Top = 0
  Width = 726
  Height = 427
  Caption = 'Dados do Questionário'
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'Tahoma'
  Font.Style = [fsBold]
  Padding.Top = 10
  Padding.Left = 10
  Padding.Right = 10
  Padding.Bottom = 5
  ParentFont = False
  TabOrder = 0
  Flex.Vflex = ftTrue
  Flex.Hflex = ftTrue
  WOwner = FrInterno
  WOrigem = EhNone
  Scrollable = False
  Closable = False
  Closed = False
  Orient = coHorizontal
  Style = grp3D
  HeaderImageId = 0
end
```

---

## TFPageControl - Controle de Abas

**Descrição:** Componente que organiza conteúdo em múltiplas abas/páginas. Essencial para formulários complexos com diferentes seções.

### Propriedades

| Propriedade | Tipo | Descrição |
|-------------|------|-----------|
| `ActivePage` | String | Nome da aba ativa inicialmente |
| `Align` | TAlign | Alinhamento no container (geralmente alClient) |
| `TabOrder` | Integer | Ordem de tabulação |
| `TabPosition` | TTabPosition | Posição das abas (tpTop, tpBottom, tpLeft, tpRight) |
| `Flex.Vflex/Hflex` | TFlexType | Flexibilidade do controle |
| `RenderStyle` | TRenderStyle | Estilo de renderização (rsTabbed para abas) |
| `WOwner`, `WOrigem` | TWOwner/TWOrigem | Configurações de ownership |

### Exemplo Prático

```pascal
object pgControlTemplates: TFPageControl
  Left = 0
  Top = 66
  Width = 748
  Height = 461
  ActivePage = tabCadastro
  Align = alClient
  TabOrder = 1
  TabPosition = tpTop
  Flex.Vflex = ftTrue
  Flex.Hflex = ftTrue
  WOwner = FrInterno
  WOrigem = EhNone
  RenderStyle = rsTabbed
end
```

---

## TFTabSheet - Página de Aba

**Descrição:** Página individual dentro de um TFPageControl. Cada aba representa uma seção do formulário.

### Propriedades

| Propriedade | Tipo | Descrição |
|-------------|------|-----------|
| `Caption` | String | Título exibido na aba |
| `Visible` | Boolean | Visibilidade da aba (True/False) |
| `Closable` | Boolean | Permitir fechar a aba pelo usuário |
| `WOwner`, `WOrigem` | TWOwner/TWOrigem | Configurações de ownership |

### Exemplo Prático

```pascal
object tabCadastro: TFTabsheet
  Caption = 'Cadastro'
  Visible = True
  Closable = False
  WOwner = FrInterno
  WOrigem = EhNone
end
```

---

## TFLabel - Rótulo/Label

**Descrição:** Componente para exibir texto estático ou vinculado a dados. Usado para labels de campos e informações.

### Propriedades

| Propriedade | Tipo | Descrição |
|-------------|------|-----------|
| `Caption` | String | Texto fixo exibido no label |
| `Left`, `Top`, `Width`, `Height` | Integer | Posição e dimensões |
| `Font.Charset/Color/Height/Name/Style` | TFont | Configurações da fonte |
| `ParentFont` | Boolean | Herdar fonte do pai |
| `FieldName` | String | Campo da tabela vinculado (para dados dinâmicos) |
| `Table` | TFTable | Tabela de origem dos dados |
| `Align` | TAlign | Alinhamento (alRight, alLeft, alClient) |
| `Anchors` | TAnchors | Âncoras de posicionamento |
| `VerticalAlignment` | TVerticalAlignment | Alinhamento vertical (taVerticalCenter, taTop, taBottom) |
| `WordBreak` | Boolean | Quebra de palavra automática |
| `MaskType` | TMaskType | Tipo de máscara (mtText, mtNumeric, mtCurrency) |
| `WKey` | String | Chave de identificação única |

### Exemplo Prático

```pascal
object lblDescQuestionario: TFLabel
  Left = 0
  Top = 0
  Width = 119
  Height = 18
  Align = alRight
  Anchors = []
  Caption = 'Descrição:'
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -15
  Font.Name = 'Tahoma'
  Font.Style = []
  ParentFont = False
  WOwner = FrInterno
  WOrigem = EhNone
  WKey = '7000192;70002;43003'
  VerticalAlignment = taVerticalCenter
  WordBreak = False
  MaskType = mtText
end
```

---

## TFButton - Botão

**Descrição:** Componente de botão para executar ações. Componente fundamental para operações CRUD e navegação.

### Propriedades

| Propriedade | Tipo | Descrição |
|-------------|------|-----------|
| `Caption` | String | Texto exibido no botão |
| `Left`, `Top`, `Width`, `Height` | Integer | Posição e dimensões |
| `Hint` | String | Texto de dica (tooltip) |
| `Font.Charset/Color/Height/Name/Style` | TFont | Configurações de fonte |
| `ParentFont` | Boolean | Herdar fonte do pai (False para personalizar fonte) |
| `TabOrder` | Integer | Ordem de tabulação |
| `OnClick` | String | Nome do método executado ao clicar |
| `PngImage.Data` | Hexadecimal | Dados da imagem PNG em formato hexadecimal (inline) |
| `ImageId` | Integer | ID da imagem do sistema (alternativa ao PngImage) |
| `Layout` | TButtonLayout | Posição do ícone (blGlyphTop, blGlyphLeft, blGlyphRight, blGlyphBottom) |
| `Color` | TColor | Cor de fundo (clBtnFace padrão) |
| `Access` | Boolean | Controle de acesso (True/False) |
| `IconClass` | String | Classe CSS do ícone (FontAwesome: 'a fas fa-list-alt') |
| `IconReverseDirection` | Boolean | Inverter direção do ícone |
| `UploadMime` | String | Tipo MIME para upload ('image/*', 'application/*') |
| `Visible` | Boolean | Visibilidade do botão (True/False) |
| `Align` | TAlign | Alinhamento no container |
| `WOwner` | TWOwner | Proprietário (FrInterno para filhos, FrWizard para raiz) |
| `WOrigem` | TWOrigem | Origem (EhNone para filhos, EhMain para raiz) |

### Exemplo Prático

**Exemplo 1: Botão com ImageId do Sistema**
```pascal
object btnPesquisarTbQuestionario: TFButton
  Left = 60
  Top = 0
  Width = 65
  Height = 56
  Caption = 'Pesquisar'
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'Tahoma'
  Font.Style = []
  Layout = blGlyphTop
  ParentFont = False
  TabOrder = 1
  OnClick = btnPesquisarTbQuestionarioClick
  ImageId = 13
  WOwner = FrInterno
  WOrigem = EhNone
  Color = clBtnFace
  Access = False
  IconReverseDirection = False
  UploadMime = 'image/*'
end
```

**Exemplo 2: Botão com IconClass FontAwesome**
```pascal
object btnListarItens: TFButton
  Left = 0
  Top = 121
  Width = 75
  Height = 25
  Caption = 'Listar'
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'Tahoma'
  Font.Style = []
  ParentFont = False
  TabOrder = 1
  WOwner = FrInterno
  WOrigem = EhNone
  Color = clBtnFace
  Access = False
  IconClass = 'a fas fa-list-alt'
  IconReverseDirection = False
  UploadMime = 'image/*'
end
```

**Exemplo 3: Botão com Imagem PNG Inline**
```pascal
object btnCustom: TFButton
  Left = 0
  Top = 0
  Width = 80
  Height = 30
  Caption = 'Custom'
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'Tahoma'
  Font.Style = []
  ParentFont = False
  TabOrder = 2
  PngImage.Data = {
    89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D
    F8000000244944415478DA63FC0F040C34048CA3168C5A306AC1A805A3168C5A
    306AC1A80543C302008F805FB9E6FF08BA0000000049454E44AE426082}
  ImageId = 0
  WOwner = FrInterno
  WOrigem = EhNone
  Color = clBtnFace
  Access = False
  IconReverseDirection = False
  UploadMime = 'image/*'
end
```

### IDs de Imagens Comuns
- **13**: Lupa (pesquisar)
- **6**: Adicionar (novo)
- **7**: Editar
- **8**: Excluir
- **4**: Salvar
- **9**: Cancelar
- **700081**: Voltar

### Classes de Ícones FontAwesome (IconClass)

| Classe CSS | Descrição | Uso Recomendado |
|------------|-----------|----------------|
| `'a fas fa-search'` | Lupa de pesquisa | Botões de pesquisar/filtrar |
| `'a fas fa-plus'` | Sinal de mais | Botões de adicionar/incluir |
| `'a fas fa-edit'` | Ícone de edição | Botões de editar/alterar |
| `'a fas fa-trash'` | Lixeira | Botões de excluir/remover |
| `'a fas fa-save'` | Ícone de salvar | Botões de salvar/gravar |
| `'a fas fa-times'` | X de cancelar | Botões de cancelar/fechar |
| `'a fas fa-list-alt'` | Lista com detalhes | Botões de listar/visualizar |
| `'a fas fa-eye'` | Olho | Botões de visualizar/ver |
| `'a fas fa-download'` | Seta para baixo | Botões de download/exportar |
| `'a fas fa-upload'` | Seta para cima | Botões de upload/importar |
| `'a fas fa-print'` | Impressora | Botões de imprimir |
| `'a fas fa-file-pdf'` | Arquivo PDF | Relatórios PDF |
| `'a fas fa-file-excel'` | Arquivo Excel | Exportar Excel |
| `'a fas fa-cog'` | Engrenagem | Configurações |
| `'a fas fa-arrow-left'` | Seta esquerda | Voltar/anterior |
| `'a fas fa-arrow-right'` | Seta direita | Avançar/próximo |

**Nota:** O prefixo `'a'` é obrigatório nas classes FontAwesome no Freedom ZK.

---

## TFString - Campo de Texto

**Descrição:** Campo de entrada de texto simples. O componente mais utilizado para entrada de dados alfanuméricos.

### Propriedades

| Propriedade | Tipo | Descrição |
|-------------|------|-----------|
| `Table` | TFTable | Tabela vinculada ao campo |
| `FieldName` | String | Nome do campo na tabela |
| `Left`, `Top`, `Width`, `Height` | Integer | Posição e dimensões |
| `TabOrder` | Integer | Ordem de tabulação |
| `AccessLevel` | Integer | Nível de acesso (0=normal) |
| `Flex` | Boolean | Campo flexível (cresce/diminui) |
| `Required` | Boolean | Campo obrigatório |
| `Prompt` | String | Texto de placeholder |
| `Constraint.*` | TConstraint | Configurações de validação |
| `IconDirection` | TIconDirection | Direção do ícone (idLeft, idRight) |
| `CharCase` | TCharCase | Formatação (ccNormal, ccUpperCase, ccLowerCase) |
| `Pwd` | Boolean | Campo de senha (mascara caracteres) |
| `Maxlength` | Integer | Tamanho máximo do texto |
| `Align` | TAlign | Alinhamento no container |
| `Font.*` | TFont | Configurações de fonte |
| `SaveLiteralCharacter` | Boolean | Salvar caracteres literais de máscara |
| `TextAlign` | TAlignment | Alinhamento do texto (taLeft, taCenter, taRight) |
| `Hint`, `HelpCaption`, `Help` | String | Sistema de ajuda |
| `WKey` | String | Chave única de identificação |

### Exemplo Prático

```pascal
object edDescQuestionario: TFString
  Left = 133
  Top = 0
  Width = 224
  Height = 24
  Table = tbQuestionario
  FieldName = 'DESC_QUESTIONARIO'
  TabOrder = 5
  AccessLevel = 0
  Flex = True
  WOwner = FrInterno
  WOrigem = EhNone
  WKey = '7000192;70002;43003'
  Required = True
  Prompt = 'Descrição do questionário'
  Constraint.Expression = 'value is null or trim(value) = '#39#39
  Constraint.Message = 'Campo Descrição é obrigatório'
  Constraint.CheckWhen = cwImmediate
  Constraint.CheckType = ctExpression
  Constraint.FocusOnError = False
  Constraint.GroupName = 'tbQuestionarioR'
  Constraint.EnableUI = True
  Constraint.Enabled = True
  Constraint.FormCheck = False
  IconDirection = idLeft
  CharCase = ccNormal
  Pwd = False
  Maxlength = 100
  Align = alLeft
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -13
  Font.Name = 'Tahoma'
  Font.Style = []
  SaveLiteralCharacter = False
  TextAlign = taLeft
end
```

---

## TFInteger - Campo Numérico Inteiro

**Descrição:** Campo de entrada para números inteiros. Usado para códigos, quantidades e valores numéricos sem decimais.

### Propriedades

| Propriedade | Tipo | Descrição |
|-------------|------|-----------|
| `Table` | TFTable | Tabela vinculada ao campo |
| `FieldName` | String | Nome do campo na tabela |
| `Left`, `Top`, `Width`, `Height` | Integer | Posição e dimensões |
| `TabOrder` | Integer | Ordem de tabulação |
| `AccessLevel` | Integer | Nível de acesso |
| `Flex` | Boolean | Flexibilidade do campo |
| `Required` | Boolean | Campo obrigatório |
| `Prompt` | String | Texto de placeholder |
| `Constraint.*` | TConstraint | Configurações de validação |
| `IconDirection` | TIconDirection | Direção do ícone |
| `Maxlength` | Integer | Tamanho máximo (0=padrão) |
| `Font.*` | TFont | Configurações de fonte |
| `Alignment` | TAlignment | Alinhamento do texto (taRightJustify comum para números) |
| `Hint`, `HelpCaption` | String | Sistema de ajuda |
| `WOwner`, `WOrigem` | TWOwner/TWOrigem | Configurações de ownership |

### Exemplo Prático

```pascal
object edCodFabrica: TFInteger
  Left = 130
  Top = 0
  Width = 65
  Height = 24
  Table = tbQuestionario
  FieldName = 'COD_FABRICA'
  TabOrder = 0
  AccessLevel = 0
  Flex = True
  WOwner = FrInterno
  WOrigem = EhNone
  Required = False
  Prompt = 'Código da fábrica'
  Constraint.Expression = 'value is null'
  Constraint.Message = 'Campo Código da fábrica é obrigatório'
  Constraint.CheckWhen = cwImmediate
  Constraint.CheckType = ctExpression
  Constraint.FocusOnError = False
  Constraint.GroupName = 'tbQuestionario'
  Constraint.EnableUI = True
  Constraint.Enabled = False
  Constraint.FormCheck = False
  IconDirection = idLeft
  Maxlength = 0
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -13
  Font.Name = 'Tahoma'
  Font.Style = []
  Alignment = taRightJustify
end
```

---

## TFCheckBox - Caixa de Seleção

**Descrição:** Campo de seleção verdadeiro/falso. Usado para campos do tipo S/N no banco de dados e opções booleanas.

### Propriedades

| Propriedade | Tipo | Descrição |
|-------------|------|-----------|
| `Caption` | String | Texto exibido ao lado da caixa |
| `Left`, `Top`, `Width`, `Height` | Integer | Posição e dimensões |
| `Font.*` | TFont | Configurações de fonte |
| `ParentFont` | Boolean | Herdar fonte do pai |
| `TabOrder` | Integer | Ordem de tabulação |
| `Table` | TFTable | Tabela vinculada |
| `FieldName` | String | Campo da tabela |
| `CheckedValue` | String | Valor quando marcado (geralmente 'S') |
| `UncheckedValue` | String | Valor quando desmarcado (geralmente 'N') |
| `ReadOnly` | Boolean | Somente leitura |
| `VerticalAlignment` | TVerticalAlignment | Alinhamento vertical (taAlignTop, taAlignBottom) |
| `Align` | TAlign | Alinhamento no container |
| `Hint`, `HelpCaption`, `Help` | String | Sistema de ajuda |
| `WKey` | String | Chave única de identificação |

### Exemplo Prático

```pascal
object edAtivo: TFCheckBox
  Left = 0
  Top = 0
  Width = 69
  Height = 21
  Align = alClient
  Caption = 'Ativo'
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -15
  Font.Name = 'Tahoma'
  Font.Style = []
  ParentFont = False
  TabOrder = 0
  Table = tbQuestionario
  FieldName = 'ATIVO'
  CheckedValue = 'S'
  UncheckedValue = 'N'
  ReadOnly = False
  WOwner = FrInterno
  WOrigem = EhNone
  WKey = '7000192;70002;70006'
  VerticalAlignment = taAlignBottom
end
```

---

## TFMemo - Campo de Texto Multilinha

**Descrição:** Campo de entrada de texto com múltiplas linhas. Usado para observações, scripts, descrições longas e comentários.

### Propriedades

| Propriedade | Tipo | Descrição |
|-------------|------|-----------|
| `Left`, `Top`, `Width`, `Height` | Integer | Posição e dimensões |
| `CharCase` | TCharCase | Formatação de caracteres (ccNormal, ccUpperCase, ccLowerCase) |
| `Font.*` | TFont | Configurações de fonte |
| `Lines.Strings` | TStringList | Linhas de texto inicial (geralmente ('FMemo1')) |
| `Maxlength` | Integer | Tamanho máximo (0=ilimitado) |
| `ParentFont` | Boolean | Herdar fonte do pai |
| `TabOrder` | Integer | Ordem de tabulação |
| `FieldName` | String | Campo da tabela vinculado |
| `Table` | TFTable | Tabela vinculada |
| `Flex.Vflex/Hflex` | TFlexType | Flexibilidade do campo |
| `Constraint.*` | TConstraint | Configurações de validação |
| `Required` | Boolean | Campo obrigatório |
| `ScrollBars` | TScrollStyle | Barras de rolagem (ssNone, ssHorizontal, ssVertical, ssBoth) |
| `WordWrap` | Boolean | Quebra de linha automática |
| `WOwner`, `WOrigem` | TWOwner/TWOrigem | Configurações de ownership |

### Exemplo Prático

```pascal
object edScriptAbertura: TFMemo
  Left = 134
  Top = 0
  Width = 323
  Height = 66
  CharCase = ccNormal
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'Tahoma'
  Font.Style = []
  Lines.Strings = (
    'FMemo1')
  Maxlength = 0
  ParentFont = False
  TabOrder = 1
  FieldName = 'SCRIPT_ABERTURA'
  Table = tbQuestionario
  Flex.Vflex = ftFalse
  Flex.Hflex = ftTrue
  WOwner = FrInterno
  WOrigem = EhNone
  Constraint.CheckWhen = cwImmediate
  Constraint.CheckType = ctExpression
  Constraint.FocusOnError = False
  Constraint.EnableUI = True
  Constraint.Enabled = False
  Constraint.FormCheck = False
  Required = False
end
```

---

## TFTable - Cursor/Tabela

**Descrição:** Componente que representa uma tabela/cursor de dados do banco. Componente fundamental para vinculação de dados aos campos do formulário.

### Propriedades

| Propriedade | Tipo | Descrição |
|-------------|------|-----------|
| `TableName` | String | Nome da tabela no banco de dados |
| `Cursor` | String | Nome do cursor para consultas |
| `MaxRowCount` | Integer | Número máximo de registros (0=ilimitado) |
| `FieldDefs` | TFieldDefs | Definições dos campos (estrutura gerada automaticamente) |
| `DeltaMode` | TDeltaMode | Modo de controle de alterações (dmChanged) |
| `RatioBatchSize` | Integer | Tamanho do lote para processamento (padrão 20) |
| `ModelType` | TModelType | Tipo do modelo (mtCollectionModel) |
| `Left`, `Top` | Integer | Posição no designer (visual apenas) |
| `WOwner` | TWOwner | Proprietário (geralmente FrWizard) |
| `WOrigem` | TWOrigem | Origem (geralmente EhNone) |
| `WKey` | String | Chave única da tabela |

### Estrutura FieldDefs

Cada campo possui:
- `Name`: Nome do campo
- `Calculated`: Campo calculado (True/False)
- `Updatable`: Permite atualização (True/False)
- `PrimaryKey`: Campo chave primária (True/False)
- `FieldType`: Tipo do campo (ftInteger, ftString, ftDateTime, etc.)
- `JSONConfig.NullOnEmpty`: Tratar vazio como null no JSON
- `Caption`: Título do campo para interface
- `WOwner`, `WOrigem`: Configurações de ownership

### Exemplo Prático

```pascal
object tbQuestionario: TFTable
  FieldDefs = <
    item
      Name = 'COD_QUESTIONARIO'
      Calculated = False
      Updatable = False
      PrimaryKey = True
      FieldType = ftInteger
      JSONConfig.NullOnEmpty = False
      Caption = 'Código Questionário'
      WOwner = FrWizard
      WOrigem = EhNone
    end
    item
      Name = 'DESC_QUESTIONARIO'
      Calculated = False
      Updatable = False
      PrimaryKey = False
      FieldType = ftString
      JSONConfig.NullOnEmpty = False
      Caption = 'Descrição Questionário'
      WOwner = FrWizard
      WOrigem = EhNone
    end
    item
      Name = 'ATIVO'
      Calculated = False
      Updatable = False
      PrimaryKey = False
      FieldType = ftString
      JSONConfig.NullOnEmpty = False
      Caption = 'Ativo'
      WOwner = FrWizard
      WOrigem = EhNone
    end>
  TableName = 'CRM_QUESTIONARIO'
  Cursor = 'CRM_QUESTIONARIO'
  MaxRowCount = 0
  WOwner = FrWizard
  WOrigem = EhNone
  WKey = '43904;43901'
  DeltaMode = dmChanged
  RatioBatchSize = 20
  ModelType = mtCollectionModel
  Left = 11
  Top = 12
end
```

---

## TFGrid - Grade de Dados

**Descrição:** Componente para exibir dados em formato tabular com funcionalidades avançadas de paginação, ordenação e edição.

### Propriedades Principais

| Propriedade | Tipo | Descrição |
|-------------|------|-----------|
| `Table` | TFTable | Tabela de dados vinculada |
| `Left`, `Top`, `Width`, `Height` | Integer | Posição e dimensões |
| `TabOrder` | Integer | Ordem de tabulação |
| `TitleFont.*` | TFont | Fonte dos títulos das colunas |
| `Flex.Vflex/Hflex` | TFlexType | Flexibilidade da grade |
| `Paging.Enabled` | Boolean | Habilitar paginação |
| `Paging.PageSize` | Integer | Número de registros por página |
| `Paging.DbPaging` | Boolean | Paginação no banco de dados |
| `FrozenColumns` | Integer | Número de colunas congeladas |
| `ShowFooter/ShowHeader` | Boolean | Exibir rodapé/cabeçalho |
| `MultiSelection` | Boolean | Permitir seleção múltipla |
| `Grouping.Enabled` | Boolean | Habilitar agrupamento |
| `EnablePopup` | Boolean | Habilitar menu de contexto |
| `EditionEnabled` | Boolean | Permitir edição inline |
| `ActionButtons.*` | Boolean | Botões de ação (View, Edit, Delete, etc.) |
| `NoBorder` | Boolean | Remover borda da grade |

### Configuração de Colunas

Cada coluna possui:
- `FieldName`: Nome do campo da tabela
- `Title.Caption`: Título da coluna
- `Width`: Largura fixa da coluna
- `Visible`: Visibilidade da coluna
- `TextAlign`: Alinhamento (taLeft, taCenter, taRight)
- `FieldType`: Tipo do campo
- `Flex`: Coluna flexível (cresce/diminui)
- `Sort`: Permitir ordenação
- `Editor.*`: Configurações do editor inline

### Exemplo Prático

```pascal
object gridOpcao: TFGrid
  Left = 0
  Top = 31
  Width = 476
  Height = 267
  TabOrder = 1
  TitleFont.Charset = DEFAULT_CHARSET
  TitleFont.Color = clWindowText
  TitleFont.Height = -11
  TitleFont.Name = 'Tahoma'
  TitleFont.Style = [fsBold]
  Table = tbQuestionario
  Flex.Vflex = ftTrue
  Flex.Hflex = ftTrue
  Paging.Enabled = True
  Paging.PageSize = 10
  Paging.DbPaging = True
  FrozenColumns = 0
  ShowFooter = False
  ShowHeader = True
  MultiSelection = False
  EnablePopup = False
  WOwner = FrInterno
  WOrigem = EhNone
  EditionEnabled = False
  NoBorder = False
  ActionButtons.BtnAccept = False
  ActionButtons.BtnView = False
  ActionButtons.BtnEdit = False
  ActionButtons.BtnDelete = False
  ActionButtons.BtnInLineEdit = False
  ActionColumn.Title = 'Ações'
  ActionColumn.Width = 100
  ActionColumn.TextAlign = taCenter
  ActionColumn.Visible = True
  Columns = <
    item
      FieldName = 'COD_QUESTIONARIO'
      Title.Caption = 'Código'
      Width = 80
      Visible = True
      TextAlign = taCenter
      FieldType = ftInteger
      Flex = False
      Sort = True
      GUID = '{9700DFD0-EE49-4EBB-B914-A9BC4A78E1D4}'
    end
    item
      FieldName = 'DESC_QUESTIONARIO'
      Title.Caption = 'Descrição'
      Visible = True
      TextAlign = taLeft
      FieldType = ftString
      Flex = True
      Sort = True
      GUID = '{24652C50-54D3-4072-97C0-350342D6CAFC}'
    end
    item
      FieldName = 'ATIVO'
      Title.Caption = 'Ativo'
      Width = 60
      Visible = True
      TextAlign = taCenter
      FieldType = ftString
      Flex = False
      ShowLabel = False
      Images = <
        item
          Expression = 'ATIVO='#39'S'#39
          EvalType = etExpression
          ImageId = 7000105
          Color = clBlack
        end
        item
          Expression = 'ATIVO='#39'N'#39' or ATIVO IS NULL'
          EvalType = etExpression
          ImageId = 7000106
          Color = clBlack
        end>
      GUID = '{BDB0AB54-E0A9-447A-A805-E2B05F83F519}'
    end>
end
```

---

## Sistema de Validação (Constraints)

### Configuração de Constraints

As validações são configuradas através das propriedades `Constraint.*`:

| Propriedade | Descrição |
|-------------|-----------|
| `Expression` | Expressão lógica para validação |
| `Message` | Mensagem de erro exibida |
| `CheckWhen` | Momento da validação (cwImmediate, cwFocusOut) |
| `CheckType` | Tipo de verificação (ctExpression) |
| `FocusOnError` | Focar no campo quando há erro |
| `GroupName` | Grupo de validação |
| `EnableUI` | Habilitar interface de validação |
| `Enabled` | Constraint ativo |
| `FormCheck` | Verificar no nível do formulário |

### Exemplos de Validação

**Campo Obrigatório:**
```pascal
Constraint.Expression = 'value is null or trim(value) = '#39#39
Constraint.Message = 'Campo é obrigatório'
Constraint.CheckWhen = cwImmediate
Constraint.CheckType = ctExpression
Constraint.Enabled = True
```

**Validação Condicional:**
```pascal
Constraint.Expression = 'value > 0'
Constraint.Message = 'Valor deve ser maior que zero'
Constraint.CheckWhen = cwFocusOut
Constraint.CheckType = ctExpression
```

---

## Imagens e Ícones em Grids

### Configuração de Imagens Condicionais

```pascal
Images = <
  item
    Expression = 'ATIVO='#39'S'#39
    EvalType = etExpression
    ImageId = 7000105  // Verde (ativo)
    Color = clBlack
  end
  item
    Expression = 'ATIVO='#39'N'#39' or ATIVO IS NULL'
    EvalType = etExpression
    ImageId = 7000106  // Vermelho (inativo)
    Color = clBlack
  end>
```

### IDs de Imagens Padrão

| ImageId | Descrição | Uso |
|---------|-----------|-----|
| 7000105 | Ícone verde (ativo) | Status ativo |
| 7000106 | Ícone vermelho (inativo) | Status inativo |
| 13 | Lupa | Pesquisar |
| 6 | Adicionar | Novo registro |
| 7 | Editar | Editar registro |
| 8 | Excluir | Excluir registro |
| 4 | Salvar | Salvar alterações |
| 9 | Cancelar | Cancelar operação |

---

## Padrões de Nomenclatura

### Convenções de Nomes

| Prefixo | Tipo de Componente | Exemplo |
|---------|-------------------|---------|
| `Frm` | Formulários | `FrmCrmQuestionario` |
| `FVBox`, `vbox` | Containers verticais | `vboxPrincipal` |
| `hBox` | Containers horizontais | `hBoxBotoes` |
| `groupbox` | Caixas de grupo | `groupboxtbQuestionario` |
| `pgControl` | Controles de páginas | `pgControlTemplates` |
| `tab` | Páginas/abas | `tabCadastro` |
| `ed` | Campos de edição | `edDescQuestionario` |
| `ft` | Campos de filtro | `ftCodQuestionario` |
| `lbl` | Labels | `lblDescQuestionario` |
| `btn` | Botões | `btnPesquisarTbQuestionario` |
| `tb` | Tabelas | `tbQuestionario` |
| `grid` | Grids | `gridOpcao` |

### Padrões de Eventos

| Padrão | Descrição |
|--------|-----------|
| `btnPesquisar*Click` | Pesquisar registros |
| `btnIncluir*Click` | Incluir novo registro |
| `btnEditar*Click` | Editar registro atual |
| `btnExcluir*Click` | Excluir registro atual |
| `btnSalvar*Click` | Salvar alterações |
| `btnCancelar*Click` | Cancelar operação |

---

## Boas Práticas de Implementação

### Layout e Responsividade

1. **Use `Flex.Vflex = ftTrue` e `Flex.Hflex = ftTrue`** em containers principais
2. **Configure `Spacing`** adequadamente (5-8px para espaçamento normal)
3. **Use `Padding`** para espaçamento interno (5-10px)
4. **Configure `Align = alClient`** para componentes que ocupam espaço total
5. **Use `Scrollable = True`** em containers que podem exceder a tela
6. **Use `HBox com HFlex = ftTrue`** para alinhar itens a esquerda ou a direita
7. **Use dois `HBox com HFlex = ftTrue`** para centralizar itens, um há esquerda e outro a direita dos itens que devem ser centralizados

### Vinculação de Dados

1. **Sempre configure `Table` e `FieldName`** em campos de dados
2. **Use `Required = True`** para campos obrigatórios
3. **Configure `WKey`** para identificação única
4. **Implemente `Constraints`** para validações
5. **Use `Prompt`** para placeholders informativos

### Performance

1. **Configure `Paging.Enabled = True`** em grids grandes
2. **Use `MaxRowCount`** para limitar consultas
3. **Configure `RatioBatchSize`** adequadamente (10-50)
4. **Evite muitos campos em grids** (máximo 8-10 colunas visíveis)

### Usabilidade

1. **Configure `TabOrder`** sequencialmente
2. **Use `Hint`** para tooltips informativos
3. **Agrupe campos relacionados** em GroupBox
4. **Use `Layout = blGlyphTop`** para botões com ícone e texto
5. **Configure `VAlign = tvTop`** em containers horizontais

### Manutenibilidade

1. **Use nomes descritivos** para componentes
2. **Mantenha consistência** nos padrões de layout
3. **Documente validações complexas**
4. **Use comentários** para seções específicas
5. **Teste em diferentes resoluções** de tela
