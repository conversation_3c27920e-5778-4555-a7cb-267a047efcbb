package freedom.bytecode.form.wizard;
import freedom.client.controls.delg.*;
import freedom.client.event.EventListener;
import freedom.data.Value;
import freedom.acesso.IAccessValidator;
import freedom.acesso.AccessValidatorFactory;
import freedom.bytecode.rn.wizard.*;
import freedom.workflow.IWorkFlow;
import freedom.workflow.WorkFlowFactory;
import freedom.client.util.ExceptionEngine;
import org.zkoss.zk.ui.Component;
import org.zkoss.zk.ui.select.annotation.Wire;
import org.zkoss.zul.Window;
import java.util.HashMap;
import java.util.Map;
import org.zkoss.zk.ui.Executions;
import freedom.client.event.*;
import freedom.data.DataException;
import freedom.bytecode.cursor.*;
import freedom.client.controls.impl.*;
import java.rmi.registry.*;
import freedom.commons.lang.IWorkList;
import freedom.util.WorkListFactory;
public abstract class FrmEmBrancoParaTestes extends TFForm  {

    private static final long serialVersionUID = 20130827081850L;
    protected freedom.bytecode.rn.EmBrancoParaTestesRNA rn = null;

    public FrmEmBrancoParaTestes() {
        try {
            rn = (freedom.bytecode.rn.EmBrancoParaTestesRNA) getRN(freedom.bytecode.rn.wizard.EmBrancoParaTestesRN.class);
        } catch (DataException e) {
            ExceptionEngine.register(e);
        }
        init_tbClienteDiverso();
        init_FVBox1();
        init_gridClienteDiverso();
        init_hBoxTotal();
        init_lblTotalTexto();
        init_lblTotalValor();
        init_btnCarregar();
        init_FrmEmBrancoParaTestes();
    }

    public CLIENTE_DIVERSO tbClienteDiverso;

    private void init_tbClienteDiverso() {
        tbClienteDiverso = rn.tbClienteDiverso;
        tbClienteDiverso.setName("tbClienteDiverso");
        tbClienteDiverso.setMaxRowCount(200);
        tbClienteDiverso.setWKey("572025;57201");
        tbClienteDiverso.setRatioBatchSize(20);
        getTables().put(tbClienteDiverso, "tbClienteDiverso");
        tbClienteDiverso.applyProperties();
    }

    protected TFForm FrmEmBrancoParaTestes = this;
    private void init_FrmEmBrancoParaTestes() {
        FrmEmBrancoParaTestes.setName("FrmEmBrancoParaTestes");
        FrmEmBrancoParaTestes.setCaption("EmBrancoParaTestes");
        FrmEmBrancoParaTestes.setClientHeight(295);
        FrmEmBrancoParaTestes.setClientWidth(449);
        FrmEmBrancoParaTestes.setColor("clBtnFace");
        FrmEmBrancoParaTestes.setWOrigem("EhMain");
        FrmEmBrancoParaTestes.setWKey("572025");
        FrmEmBrancoParaTestes.setSpacing(0);
        FrmEmBrancoParaTestes.applyProperties();
    }

    public TFVBox FVBox1 = new TFVBox();

    private void init_FVBox1() {
        FVBox1.setName("FVBox1");
        FVBox1.setLeft(0);
        FVBox1.setTop(0);
        FVBox1.setWidth(449);
        FVBox1.setHeight(295);
        FVBox1.setAlign("alClient");
        FVBox1.setBorderStyle("stNone");
        FVBox1.setPaddingTop(5);
        FVBox1.setPaddingLeft(5);
        FVBox1.setPaddingRight(5);
        FVBox1.setPaddingBottom(5);
        FVBox1.setMarginTop(0);
        FVBox1.setMarginLeft(0);
        FVBox1.setMarginRight(0);
        FVBox1.setMarginBottom(0);
        FVBox1.setSpacing(5);
        FVBox1.setFlexVflex("ftTrue");
        FVBox1.setFlexHflex("ftTrue");
        FVBox1.setScrollable(false);
        FVBox1.setBoxShadowConfigHorizontalLength(10);
        FVBox1.setBoxShadowConfigVerticalLength(10);
        FVBox1.setBoxShadowConfigBlurRadius(5);
        FVBox1.setBoxShadowConfigSpreadRadius(0);
        FVBox1.setBoxShadowConfigShadowColor("clBlack");
        FVBox1.setBoxShadowConfigOpacity(75);
        FrmEmBrancoParaTestes.addChildren(FVBox1);
        FVBox1.applyProperties();
    }

    public TFGrid gridClienteDiverso = new TFGrid();

    private void init_gridClienteDiverso() {
        gridClienteDiverso.setName("gridClienteDiverso");
        gridClienteDiverso.setLeft(0);
        gridClienteDiverso.setTop(0);
        gridClienteDiverso.setWidth(439);
        gridClienteDiverso.setHeight(250);
        gridClienteDiverso.setTable(tbClienteDiverso);
        gridClienteDiverso.setFlexVflex("ftTrue");
        gridClienteDiverso.setFlexHflex("ftTrue");
        gridClienteDiverso.setPagingEnabled(true);
        gridClienteDiverso.setFrozenColumns(0);
        gridClienteDiverso.setShowFooter(false);
        gridClienteDiverso.setShowHeader(true);
        gridClienteDiverso.setMultiSelection(false);
        gridClienteDiverso.setGroupingEnabled(false);
        gridClienteDiverso.setGroupingExpanded(false);
        gridClienteDiverso.setGroupingShowFooter(false);
        gridClienteDiverso.setCrosstabEnabled(false);
        gridClienteDiverso.setCrosstabGroupType("cgtConcat");
        gridClienteDiverso.setEditionEnabled(false);
        gridClienteDiverso.setNoBorder(false);
        TFGridColumn item0 = new TFGridColumn();
        item0.setFieldName("COD_CLIENTE");
        item0.setTitleCaption("C\u00F3d. Cliente");
        item0.setWidth(120);
        item0.setVisible(true);
        item0.setPrecision(0);
        item0.setTextAlign("taCenter");
        item0.setFieldType("ftInteger");
        item0.setFlexRatio(0);
        item0.setSort(true);
        item0.setImageHeader(0);
        item0.setWrap(false);
        item0.setFlex(false);
        item0.setCharCase("ccNormal");
        item0.setBlobConfigMimeType("bmtText");
        item0.setBlobConfigShowType("btImageViewer");
        item0.setShowLabel(true);
        item0.setEditorEditType("etTFString");
        item0.setEditorPrecision(0);
        item0.setEditorMaxLength(100);
        item0.setEditorLookupFilterKey(0);
        item0.setEditorLookupFilterDesc(0);
        item0.setEditorPopupHeight(400);
        item0.setEditorPopupWidth(400);
        item0.setEditorCharCase("ccNormal");
        item0.setEditorEnabled(false);
        item0.setEditorReadOnly(false);
        item0.setCheckedValue("S");
        item0.setUncheckedValue("N");
        item0.setHiperLink(false);
        item0.setEditorConstraintCheckWhen("cwImmediate");
        item0.setEditorConstraintCheckType("ctExpression");
        item0.setEditorConstraintFocusOnError(false);
        item0.setEditorConstraintEnableUI(true);
        item0.setEditorConstraintEnabled(false);
        item0.setEmpty(false);
        item0.setMobileOptsShowMobile(false);
        item0.setMobileOptsOrder(0);
        item0.setBoxSize(0);
        item0.setImageSrcType("istSource");
        gridClienteDiverso.getColumns().add(item0);
        TFGridColumn item1 = new TFGridColumn();
        item1.setFieldName("NOME");
        item1.setTitleCaption("Nome");
        item1.setVisible(true);
        item1.setPrecision(0);
        item1.setTextAlign("taLeft");
        item1.setFieldType("ftString");
        item1.setFlexRatio(0);
        item1.setSort(true);
        item1.setImageHeader(0);
        item1.setWrap(false);
        item1.setFlex(true);
        item1.setCharCase("ccNormal");
        item1.setBlobConfigMimeType("bmtText");
        item1.setBlobConfigShowType("btImageViewer");
        item1.setShowLabel(true);
        item1.setEditorEditType("etTFString");
        item1.setEditorPrecision(0);
        item1.setEditorMaxLength(100);
        item1.setEditorLookupFilterKey(0);
        item1.setEditorLookupFilterDesc(0);
        item1.setEditorPopupHeight(400);
        item1.setEditorPopupWidth(400);
        item1.setEditorCharCase("ccNormal");
        item1.setEditorEnabled(false);
        item1.setEditorReadOnly(false);
        item1.setCheckedValue("S");
        item1.setUncheckedValue("N");
        item1.setHiperLink(false);
        item1.setEditorConstraintCheckWhen("cwImmediate");
        item1.setEditorConstraintCheckType("ctExpression");
        item1.setEditorConstraintFocusOnError(false);
        item1.setEditorConstraintEnableUI(true);
        item1.setEditorConstraintEnabled(false);
        item1.setEmpty(false);
        item1.setMobileOptsShowMobile(false);
        item1.setMobileOptsOrder(0);
        item1.setBoxSize(0);
        item1.setImageSrcType("istSource");
        gridClienteDiverso.getColumns().add(item1);
        FVBox1.addChildren(gridClienteDiverso);
        gridClienteDiverso.applyProperties();
    }

    public TFHBox hBoxTotal = new TFHBox();

    private void init_hBoxTotal() {
        hBoxTotal.setName("hBoxTotal");
        hBoxTotal.setLeft(0);
        hBoxTotal.setTop(251);
        hBoxTotal.setWidth(439);
        hBoxTotal.setHeight(25);
        hBoxTotal.setBorderStyle("stNone");
        hBoxTotal.setPaddingTop(2);
        hBoxTotal.setPaddingLeft(5);
        hBoxTotal.setPaddingRight(5);
        hBoxTotal.setPaddingBottom(2);
        hBoxTotal.setMarginTop(0);
        hBoxTotal.setMarginLeft(0);
        hBoxTotal.setMarginRight(0);
        hBoxTotal.setMarginBottom(0);
        hBoxTotal.setSpacing(5);
        hBoxTotal.setFlexVflex("ftFalse");
        hBoxTotal.setFlexHflex("ftTrue");
        hBoxTotal.setScrollable(false);
        hBoxTotal.setBoxShadowConfigHorizontalLength(10);
        hBoxTotal.setBoxShadowConfigVerticalLength(10);
        hBoxTotal.setBoxShadowConfigBlurRadius(5);
        hBoxTotal.setBoxShadowConfigSpreadRadius(0);
        hBoxTotal.setBoxShadowConfigShadowColor("clBlack");
        hBoxTotal.setBoxShadowConfigOpacity(75);
        hBoxTotal.setVAlign("tvTop");
        FVBox1.addChildren(hBoxTotal);
        hBoxTotal.applyProperties();
    }

    public TFLabel lblTotalTexto = new TFLabel();

    private void init_lblTotalTexto() {
        lblTotalTexto.setName("lblTotalTexto");
        lblTotalTexto.setLeft(0);
        lblTotalTexto.setTop(0);
        lblTotalTexto.setWidth(26);
        lblTotalTexto.setHeight(13);
        lblTotalTexto.setAlign("alLeft");
        lblTotalTexto.setCaption("total:");
        lblTotalTexto.setFontColor("clWindowText");
        lblTotalTexto.setFontSize(-11);
        lblTotalTexto.setFontName("Tahoma");
        lblTotalTexto.setFontStyle("[]");
        lblTotalTexto.setVerticalAlignment("taVerticalCenter");
        lblTotalTexto.setWordBreak(false);
        hBoxTotal.addChildren(lblTotalTexto);
        lblTotalTexto.applyProperties();
    }

    public TFLabel lblTotalValor = new TFLabel();

    private void init_lblTotalValor() {
        lblTotalValor.setName("lblTotalValor");
        lblTotalValor.setLeft(26);
        lblTotalValor.setTop(0);
        lblTotalValor.setWidth(6);
        lblTotalValor.setHeight(13);
        lblTotalValor.setAlign("alLeft");
        lblTotalValor.setCaption("0");
        lblTotalValor.setFontColor("clWindowText");
        lblTotalValor.setFontSize(-11);
        lblTotalValor.setFontName("Tahoma");
        lblTotalValor.setFontStyle("[]");
        lblTotalValor.setVerticalAlignment("taVerticalCenter");
        lblTotalValor.setWordBreak(false);
        hBoxTotal.addChildren(lblTotalValor);
        lblTotalValor.applyProperties();
    }

    public TFButton btnCarregar = new TFButton();

    private void init_btnCarregar() {
        btnCarregar.setName("btnCarregar");
        btnCarregar.setUploadMime("image/*");
        btnCarregar.setLeft(32);
        btnCarregar.setTop(0);
        btnCarregar.setWidth(80);
        btnCarregar.setHeight(21);
        btnCarregar.setAlign("alRight");
        btnCarregar.setCaption("Carregar");
        btnCarregar.setFontColor("clWindowText");
        btnCarregar.setFontSize(-11);
        btnCarregar.setFontName("Tahoma");
        btnCarregar.setFontStyle("[]");
        btnCarregar.setImageId(0);
        btnCarregar.setColor("clBtnFace");
        btnCarregar.setAccess(false);
        btnCarregar.setIconClass("a fas fa-sync");
        btnCarregar.setIconReverseDirection(false);        hBoxTotal.addChildren(btnCarregar);
        btnCarregar.applyProperties();
    }

    protected final void processarFlow(String nomeForm, String nomeComp, String action) {
        IWorkFlow flow = WorkFlowFactory.getInstance();
        flow.continueFlow(nomeForm, nomeComp, action);
    }

    @SuppressWarnings("unchecked")
    protected final <T extends freedom.commons.lang.IRegraNegocio> T getRN(Class<T> rn) throws DataException {
        try {
            try {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("U")) {
                    cn += "U";
                }
                return (T) Class.forName(cn).newInstance();
            } catch (ClassNotFoundException e) {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("A")) {
                    cn += "A";
                }
                return (T) Class.forName(cn).newInstance();
            }
        } catch (InstantiationException | IllegalAccessException | ClassNotFoundException e) {
            throw new DataException(e.getMessage(), e);
        }
    }

}