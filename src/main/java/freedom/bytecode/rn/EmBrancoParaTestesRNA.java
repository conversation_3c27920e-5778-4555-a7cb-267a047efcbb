package freedom.bytecode.rn;
import freedom.commons.lang.IRegraNegocio;
import freedom.connection.ISession;
import freedom.data.RowState;
import freedom.data.impl.DeltaTO;
import java.rmi.Remote;
import java.rmi.RemoteException;
import freedom.client.event.*;
import freedom.data.DataException;
import freedom.bytecode.cursor.*;
import freedom.client.controls.impl.*;
import java.rmi.registry.*;
import freedom.commons.lang.IWorkList;
import freedom.data.impl.RowType;
import freedom.util.TableUtil;
import freedom.util.WorkListFactory;

import freedom.bytecode.rn.wizard.EmBrancoParaTestesRNW;
import lombok.Getter;
import lombok.Setter;

public class EmBrancoParaTestesRNA extends EmBrancoParaTestesRNW {
    private static final long serialVersionUID = 20130827081850L;


}
